import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/supabase_service.dart';

class UploadService {
  final SupabaseClient _supabase = SupabaseService.supabaseClient;
  static const String _bucketName = 'media';

  /// Create a complete post with image and text content
  Future<bool> createPost({
    required File imageFile,
    required String content,
  }) async {
    try {
      // Get current user
      final user = _supabase.auth.currentUser;
      if (user == null) {
        print('User not authenticated');
        return false;
      }

      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${user.id}_$timestamp.jpg';
      final filePath = 'uploads/$fileName';

      // Read file bytes
      final bytes = await imageFile.readAsBytes();

      // Upload to Supabase storage (using media bucket)
      await _supabase.storage.from(_bucketName).uploadBinary(filePath, bytes);

      // Get public URL
      final publicUrl = _supabase.storage
          .from(_bucketName)
          .getPublicUrl(filePath);

      // Create post record in database with content
      await _createPostWithContent(publicUrl, user.id, content);

      print('Post created successfully: $publicUrl');
      return true;
    } catch (e) {
      print('Error creating post: $e');

      // Provide more specific error messages
      if (e.toString().contains('Bucket not found')) {
        print(
          '💡 The media bucket does not exist. Please ensure your backend is running.',
        );
        print('💡 Run: docker-compose up in your backend directory');
      } else if (e.toString().contains('401') ||
          e.toString().contains('Unauthorized')) {
        print(
          '💡 Authentication error. Please check your Supabase configuration.',
        );
      } else if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        print(
          '💡 Network error. Please check your internet connection and backend URL.',
        );
      }

      return false;
    }
  }

  /// Upload an image file to Supabase storage (legacy method)
  Future<bool> uploadImage(File imageFile) async {
    return createPost(imageFile: imageFile, content: '');
  }

  /// Create a post record in the database with content
  Future<void> _createPostWithContent(
    String imageUrl,
    String userId,
    String content,
  ) async {
    try {
      await _supabase.from('posts').insert({
        'user_id': userId,
        'media_url': imageUrl, // Changed from 'image_url' to 'media_url'
        'media_type': 'image', // Added media_type
        'content': content,
        'channel_id': null, // Explicitly set to null for non-channel posts
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Error creating post record: $e');
      rethrow;
    }
  }

  /// Create a post record in the database (legacy method)
  Future<void> _createPost(String imageUrl, String userId) async {
    return _createPostWithContent(imageUrl, userId, '');
  }

  /// Upload multiple images (for future use)
  Future<List<String>> uploadMultipleImages(List<File> imageFiles) async {
    final uploadedUrls = <String>[];

    for (final file in imageFiles) {
      try {
        final success = await uploadImage(file);
        if (success) {
          // In a real implementation, you'd return the actual URL
          uploadedUrls.add('uploaded_${DateTime.now().millisecondsSinceEpoch}');
        }
      } catch (e) {
        print('Error uploading file ${file.path}: $e');
      }
    }

    return uploadedUrls;
  }

  /// Delete an uploaded image
  Future<bool> deleteImage(String filePath) async {
    try {
      await _supabase.storage.from(_bucketName).remove([filePath]);

      return true;
    } catch (e) {
      print('Error deleting image: $e');
      return false;
    }
  }

  /// Get upload progress (for future implementation)
  Stream<double> getUploadProgress() {
    // This would be implemented with a proper upload progress tracking
    // For now, return a simple stream
    return Stream.periodic(
      const Duration(milliseconds: 100),
      (count) => (count * 10).clamp(0, 100).toDouble(),
    ).take(11);
  }
}
