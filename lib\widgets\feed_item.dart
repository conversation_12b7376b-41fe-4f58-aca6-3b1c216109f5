import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/post_model.dart';
import '../theme/app_theme.dart';
import 'video_player_widget.dart';

class FeedItem extends StatefulWidget {
  final PostModel post;
  final bool isVisible;

  const FeedItem({super.key, required this.post, this.isVisible = true});

  @override
  State<FeedItem> createState() => _FeedItemState();
}

class _FeedItemState extends State<FeedItem> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: _buildMediaContent(),
    );
  }

  Widget _buildMediaContent() {
    if (widget.post.hasMedia && widget.post.mediaUrl != null) {
      if (widget.post.isImage) {
        return SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: Image.network(
            widget.post.mediaUrl!,
            fit: BoxFit.cover, // Changed from contain to cover for full screen
            errorBuilder: (context, error, stackTrace) {
              if (kDebugMode) {
                print('Error loading image: $error');
              }
              return _buildMediaPlaceholder('Image');
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value:
                      loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    AppColors.gfGreen,
                  ),
                ),
              );
            },
          ),
        );
      } else if (widget.post.isVideo) {
        // Use the video player widget
        return VideoPlayerWidget(
          videoUrl: widget.post.mediaUrl!,
          autoPlay: true,
          isVisible: widget.isVisible,
        );
      }
    }

    // Fallback to a gradient background
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
    );
  }

  Widget _buildMediaPlaceholder(String type) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type == 'Video' ? Icons.play_circle_outline : Icons.image,
              size: 64,
              color: Colors.white70,
            ),
            const SizedBox(height: 16),
            Text(
              '$type Content',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
