#!/bin/bash

# GameFlex Backend Stop Script
# This script stops the Supabase development environment

set -e

# Function to show help
show_help() {
    echo "GameFlex Backend Stop Script"
    echo "Usage: ./stop.sh [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --keep-data      Keep data volumes (by default all volumes are removed)"
    echo "  --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./stop.sh                    # Stop services and remove all volumes (default)"
    echo "  ./stop.sh --keep-data        # Stop services but keep data volumes"
}

# Parse command line arguments
KEEP_DATA=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --keep-data)
            KEEP_DATA=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

echo "🛑 Stopping GameFlex Development Backend..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Services may already be stopped."
    exit 0
fi

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml not found. Make sure you're in the backend directory."
    exit 1
fi

if [ "$KEEP_DATA" = true ]; then
    echo "🐳 Stopping Docker containers (keeping data volumes)..."
    docker-compose down
    echo "✅ All services stopped! Data volumes preserved."
else
    echo "🗑️  Stopping containers and removing all volumes..."
    docker-compose down -v
    echo "✅ All services stopped and volumes removed!"
fi

echo ""
echo "🔧 Next Steps:"
echo "   To start again: ./start.sh"

if [ "$KEEP_DATA" = true ]; then
    echo "   To remove all data: ./stop.sh"
fi

echo "   To view logs: docker-compose logs"
echo ""
