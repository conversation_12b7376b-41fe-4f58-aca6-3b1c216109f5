import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();

  SupabaseService._();

  // Supabase configuration - these should match your backend/.env file
  // Smart URL selection for different network environments
  static String get supabaseUrl {
    // For Android emulator, use ******** which maps to host machine's localhost
    // This is the standard way to access host services from Android emulator
    return 'http://********:8000';
  }

  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE';

  /// Transform URLs to use ******** for Android emulator compatibility
  /// This ensures all URLs work properly in the Android emulator environment
  /// ******** is the special IP that maps to the host machine's localhost
  static String? transformUrl(String? url) {
    if (url == null || url.isEmpty) return url;

    String transformedUrl = url;

    // Replace localhost with ******** (for Android emulator)
    transformedUrl = transformedUrl.replaceAll(
      'localhost:8000',
      '********:8000',
    );

    // Replace gameflex.local with ******** (for Android emulator compatibility)
    transformedUrl = transformedUrl.replaceAll(
      'gameflex.local:8000',
      '********:8000',
    );

    // Replace api.gameflex.local with ******** (for Android emulator compatibility)
    transformedUrl = transformedUrl.replaceAll(
      'api.gameflex.local:8000',
      '********:8000',
    );

    return transformedUrl;
  }

  SupabaseClient get client => Supabase.instance.client;

  // Static getter for client access
  static SupabaseClient get supabaseClient => Supabase.instance.client;

  static Future<void> initialize() async {
    try {
      developer.log('SupabaseService: Initializing with URL: $supabaseUrl');
      if (kDebugMode) {
        print('SupabaseService: Initializing Supabase...');
        print('SupabaseService: URL: $supabaseUrl');
      }

      await Supabase.initialize(
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
        debug: true, // Enable debug mode for development
      );

      developer.log('SupabaseService: Successfully initialized');
      if (kDebugMode) {
        print('SupabaseService: Successfully initialized');
      }
    } catch (e, stackTrace) {
      developer.log(
        'SupabaseService: Failed to initialize',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('SupabaseService: INITIALIZATION ERROR: $e');
        print('SupabaseService: INITIALIZATION STACK TRACE: $stackTrace');
      }
      rethrow;
    }
  }

  // Get current user
  User? get currentUser => client.auth.currentUser;

  // Get current session
  Session? get currentSession => client.auth.currentSession;

  // Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  // Get auth stream for listening to auth state changes
  Stream<AuthState> get authStateChanges => client.auth.onAuthStateChange;

  // Test database connection
  Future<bool> testConnection() async {
    try {
      developer.log('SupabaseService: Testing database connection...');
      if (kDebugMode) {
        print('SupabaseService: Testing database connection...');
      }

      // Try a simple query to test the connection
      final response = await client.from('posts').select('count').limit(1);

      developer.log(
        'SupabaseService: Connection test successful - Response: $response',
      );
      if (kDebugMode) {
        print(
          'SupabaseService: Connection test successful - Response: $response',
        );
      }
      return true;
    } catch (e, stackTrace) {
      developer.log(
        'SupabaseService: Connection test failed',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('SupabaseService: CONNECTION TEST FAILED: $e');
        print('SupabaseService: CONNECTION TEST STACK TRACE: $stackTrace');
      }
      return false;
    }
  }
}
