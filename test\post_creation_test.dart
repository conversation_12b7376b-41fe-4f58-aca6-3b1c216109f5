import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:supabase/supabase.dart';

/// Creates a simple black JPEG image with 9:16 aspect ratio (180x320 pixels)
Uint8List _createBlackJPEG() {
  // Minimal valid JPEG file (black 180x320 image)
  // This is a pre-generated minimal JPEG for testing purposes
  return Uint8List.fromList([
    // JPEG header
    0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
    0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
    0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
    0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
    0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
    0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
    0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
    0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x01, 0x40,
    0x00, 0xB4, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
    0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
    0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
    0x03,
    0x01,
    0x00,
    0x02,
    0x11,
    0x03,
    0x11,
    0x00,
    0x3F,
    0x00,
    0x00,
    0xFF,
    0xD9,
  ]);
}

void main() {
  group('Post Creation Tests', () {
    late SupabaseClient client;

    setUpAll(() async {
      // Initialize Supabase client directly for testing (no Flutter dependencies)
      client = SupabaseClient(
        'http://localhost:8000',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE',
      );
    });

    test('Test Database Connection', () async {
      try {
        final response = await client.from('posts').select('count').count();
        print(
          '✅ Database connection successful. Post count: ${response.count}',
        );
        expect(response.count, isA<int>());
      } catch (e) {
        print('❌ Database connection failed: $e');
        fail('Database connection failed: $e');
      }
    });

    test('Test Post Creation', () async {
      expect(client, isNotNull);

      // Test basic connectivity
      try {
        final response = await client.from('posts').select('count').count();
        print('✅ Database connection successful. Post count: $response');
      } catch (e) {
        print('❌ Database connection failed: $e');
        fail('Database connection failed');
      }
    });

    test('Test Storage Bucket Access', () async {
      try {
        final buckets = await client.storage.listBuckets();
        print('✅ Storage connection successful');
        print('Available buckets: ${buckets.map((b) => b.name).join(', ')}');

        // Note: listBuckets() may return empty list due to permissions, but storage works
        // We'll test actual storage functionality in the photo upload test
        expect(buckets, isA<List>());
        print('✅ Storage service accessible');
      } catch (e) {
        print('❌ Storage connection failed: $e');
        fail('Storage connection failed');
      }
    });

    test('Test Authentication', () async {
      try {
        // Try to sign in with test credentials
        final response = await client.auth.signInWithPassword(
          email: '<EMAIL>',
          password: 'password123',
        );

        if (response.user != null) {
          print('✅ Authentication successful');
          print('User ID: ${response.user!.id}');
          print('User Email: ${response.user!.email}');
        } else {
          print('⚠️  Authentication returned null user');
        }
      } catch (e) {
        print('❌ Authentication failed: $e');
        // Don't fail the test as auth might not be set up yet
      }
    });

    test('Test Post Creation (Mock)', () async {
      // First authenticate to get a valid user ID for RLS
      try {
        final authResponse = await client.auth.signInWithPassword(
          email: '<EMAIL>',
          password: 'password123',
        );

        if (authResponse.user == null) {
          print('⚠️  Skipping post creation test - authentication failed');
          return;
        }

        final testPost = {
          'user_id': authResponse.user!.id,
          'content': 'Test post created by Flutter test',
          'media_type': 'image',
          'media_url':
              'http://localhost:8000/storage/v1/object/public/media/test.jpg',
          'channel_id': null,
        };

        final response = await client.from('posts').insert(testPost).select();

        if (response.isNotEmpty) {
          print('✅ Post creation successful');
          print('Created post ID: ${response.first['id']}');

          // Clean up - delete the test post
          await client.from('posts').delete().eq('id', response.first['id']);
          print('✅ Test post cleaned up');
        } else {
          print('❌ Post creation returned empty response');
          fail('Post creation failed');
        }
      } catch (e) {
        print('❌ Post creation failed: $e');
        fail('Post creation failed: $e');
      }
    });

    test('Test Real Photo Upload with Authentication', () async {
      try {
        // First authenticate with test user
        print('🔐 Authenticating test user...');
        final authResponse = await client.auth.signInWithPassword(
          email: '<EMAIL>',
          password: 'password123',
        );

        if (authResponse.user == null) {
          print('❌ Authentication failed - cannot test photo upload');
          fail('Authentication required for photo upload test');
        }

        print('✅ Authenticated as: ${authResponse.user!.email}');
        print('User ID: ${authResponse.user!.id}');

        // Create a test image file (9:16 black JPEG - 180x320 pixels)
        final testImageBytes = _createBlackJPEG();

        // Create temporary file
        final tempDir = Directory.systemTemp;
        final testImageFile = File('${tempDir.path}/test_image.jpg');
        await testImageFile.writeAsBytes(testImageBytes);

        print('📸 Created test image file: ${testImageFile.path}');

        // Attempt to upload the image directly to storage
        print('📤 Attempting photo upload...');
        final user = authResponse.user!;
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final fileName = '${user.id}_$timestamp.jpg';
        final filePath = 'uploads/$fileName';

        // Upload to Supabase storage
        await client.storage
            .from('media')
            .uploadBinary(filePath, testImageBytes);
        print('✅ Image uploaded successfully');

        // Get public URL
        final publicUrl = client.storage.from('media').getPublicUrl(filePath);
        print('🔗 Public URL: $publicUrl');

        // Create post with uploaded image
        print('📝 Creating post with image...');
        final postResponse =
            await client
                .from('posts')
                .insert({
                  'user_id': user.id,
                  'media_url': publicUrl,
                  'media_type': 'image',
                  'content': 'Test post with real photo upload',
                  'channel_id': null,
                })
                .select()
                .single();

        print('✅ Post created successfully!');
        print('Post ID: ${postResponse['id']}');

        // Clean up - delete the test post and image
        print('🧹 Cleaning up...');
        await client.from('posts').delete().eq('id', postResponse['id']);
        await client.storage.from('media').remove([filePath]);

        // Clean up the temporary file
        if (await testImageFile.exists()) {
          await testImageFile.delete();
        }

        print('✅ Test completed successfully!');
        expect(postResponse['id'], isNotNull);
      } catch (e) {
        print('❌ Photo upload test failed with error: $e');

        // Provide specific guidance based on error type
        if (e.toString().contains('storage') ||
            e.toString().contains('bucket')) {
          print('💡 This appears to be a storage bucket or RLS policy issue');
          print('💡 Check that storage policies exist for the media bucket');
        } else if (e.toString().contains('auth') ||
            e.toString().contains('401')) {
          print('💡 This appears to be an authentication issue');
          print(
            '💡 Check that the test user exists and credentials are correct',
          );
        }

        fail('Photo upload test failed: $e');
      }
    });
  });
}
